"""
Self-contained Attendance Reporter module for VenusHR14 database.
Generates attendance reports based on data from the HR_T_TAMachine_Summary table.
"""

import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import calendar
import pyodbc
import json

# Import our internal modules
try:
    from .db_connection import DatabaseConnection
except ImportError:
    from db_connection import DatabaseConnection
try:
    from .export_manager import ExportManager
except ImportError:
    from export_manager import ExportManager

# Configure logging
logger = logging.getLogger(__name__)

class AttendanceReporter:
    """
    Self-contained attendance reporter with built-in database connection management.
    Generates attendance reports based on data from HR_T_TAMachine_Summary.
    """

    def __init__(self, config_file: str = None):
        """
        Initialize the attendance reporter.
        
        Args:
            config_file: Path to configuration file
        """
        try:
            self.db_connection = DatabaseConnection(config_file)
            self.export_manager = ExportManager(
                export_dir=os.path.abspath(os.path.join(os.path.dirname(__file__), '../exports'))
            )
            
            # Create exports directory if it doesn't exist
            os.makedirs(
                os.path.abspath(os.path.join(os.path.dirname(__file__), '../exports')), 
                exist_ok=True
            )
            
            # Load national holidays
            self.national_holidays = self._load_national_holidays()
            
            logger.info("AttendanceReporter initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize AttendanceReporter: {str(e)}")
            raise

    def _load_national_holidays(self):
        """Load national holidays from JSON file."""
        try:
            holidays_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'national_holidays_2025.json')
            with open(holidays_file, 'r', encoding='utf-8') as f:
                holidays_data = json.load(f)
            
            # Create a set of holiday dates for fast lookup
            holiday_dates = set()
            holiday_info = {}
            for holiday in holidays_data['holidays']:
                holiday_dates.add(holiday['date'])
                holiday_info[holiday['date']] = holiday['description']
            
            logger.info(f"Loaded {len(holiday_dates)} national holidays for {holidays_data['year']}")
            return {'dates': holiday_dates, 'info': holiday_info}
        except Exception as e:
            logger.warning(f"Failed to load national holidays: {e}")
            return {'dates': set(), 'info': {}}
    
    def is_national_holiday(self, date_str):
        """Check if a date is a national holiday."""
        return date_str in self.national_holidays['dates']
    
    def get_holiday_info(self, date_str):
        """Get holiday information for a date."""
        return self.national_holidays['info'].get(date_str, None)

    def get_connection(self):
        """Get database connection for enhanced connection management."""
        return self.db_connection

    def get_attendance_data(self, start_date: str, end_date: str, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get attendance data from both attendance and overtime tables."""
        try:
            # Query 1: Get normal attendance data from HR_T_TAMachine_Summary
            attendance_query = """
            SELECT 
                t.BusCode,
                t.UserDeviceID,
                t.UserDeviceName,
                t.EmployeeID,
                t.TADate,
                t.TACheckIn,
                t.TACheckOut,
                t.Shift,
                t.IsCrossDay,
                emp.EmployeeName,
                emp.Gender
            FROM HR_T_TAMachine_Summary t
            LEFT JOIN HR_M_EmployeePI emp ON t.EmployeeID = emp.EmployeeID
            WHERE t.TADate BETWEEN ? AND ?
            """
            attendance_params = [start_date, end_date]
            if bus_code:
                attendance_query += " AND t.BusCode = ?"
                attendance_params.append(bus_code)
            attendance_query += " ORDER BY t.EmployeeID, t.TADate"
            
            # Query 2: Get overtime data from HR_T_Overtime
            overtime_query = """
            SELECT 
                ot.BusCode,
                ot.EmployeeID,
                ot.OTDate,
                ot.OTHourDuration,
                ot.OTStart,
                ot.OTFinish,
                ot.Description,
                ot.AppStatus
            FROM HR_T_Overtime ot
            WHERE ot.OTDate BETWEEN ? AND ?
            AND ot.OTHourDuration > 0
            """
            overtime_params = [start_date, end_date]
            if bus_code:
                overtime_query += " AND ot.BusCode = ?"
                overtime_params.append(bus_code)
            overtime_query += " ORDER BY ot.EmployeeID, ot.OTDate"
            
            # Execute both queries
            attendance_results = self.db_connection.execute_query(attendance_query, tuple(attendance_params))
            overtime_results = self.db_connection.execute_query(overtime_query, tuple(overtime_params))
            
            # Create overtime lookup dictionary
            overtime_lookup = {}
            for ot_record in overtime_results:
                employee_id = ot_record['EmployeeID']
                ot_date = ot_record['OTDate']
                if isinstance(ot_date, datetime):
                    ot_date_str = ot_date.strftime('%Y-%m-%d')
                else:
                    ot_date_str = str(ot_date)
                
                key = f"{employee_id}_{ot_date_str}"
                if key not in overtime_lookup:
                    overtime_lookup[key] = 0
                
                # Sum up overtime hours for the same employee and date
                ot_hours = float(ot_record.get('OTHourDuration', 0) or 0)
                overtime_lookup[key] += ot_hours
            
            # Process attendance data and combine with overtime
            combined_results = []
            for record in attendance_results:
                employee_id = record['EmployeeID']
                ta_date = record['TADate']
                
                if isinstance(ta_date, datetime):
                    ta_date_str = ta_date.strftime('%Y-%m-%d')
                    weekday = ta_date.weekday()  # 0=Monday, 6=Sunday
                else:
                    ta_date_obj = datetime.strptime(str(ta_date), '%Y-%m-%d')
                    ta_date_str = str(ta_date)
                    weekday = ta_date_obj.weekday()
                
                # Calculate normal working hours from check-in/check-out
                check_in = record.get('TACheckIn')
                check_out = record.get('TACheckOut')
                normal_hours = 0
                total_minutes = 0
                status = "absent"
                
                # Check attendance completeness
                has_check_in = check_in is not None and str(check_in).strip() != ''
                has_check_out = check_out is not None and str(check_out).strip() != ''
                
                if has_check_in and has_check_out:
                    # Complete attendance - calculate actual hours
                    try:
                        if isinstance(check_in, str) and check_in.strip():
                            # Handle string time formats like "07:33" or "07:33:44"
                            time_parts = check_in.split(':')
                            if len(time_parts) >= 2:
                                check_in_time = datetime.strptime(f"{time_parts[0]}:{time_parts[1]}", '%H:%M').time()
                            else:
                                check_in_time = datetime.strptime(check_in, '%H:%M').time()
                        elif hasattr(check_in, 'time'):
                            # datetime object
                            check_in_time = check_in.time()
                        else:
                            # time object
                            check_in_time = check_in
                            
                        if isinstance(check_out, str) and check_out.strip():
                            # Handle string time formats like "16:06" or "16:06:22"
                            time_parts = check_out.split(':')
                            if len(time_parts) >= 2:
                                check_out_time = datetime.strptime(f"{time_parts[0]}:{time_parts[1]}", '%H:%M').time()
                            else:
                                check_out_time = datetime.strptime(check_out, '%H:%M').time()
                        elif hasattr(check_out, 'time'):
                            # datetime object
                            check_out_time = check_out.time()
                        else:
                            # time object
                            check_out_time = check_out
                        
                        # Convert to datetime for calculation
                        base_date = ta_date.date() if isinstance(ta_date, datetime) else datetime.strptime(ta_date_str, '%Y-%m-%d').date()
                        check_in_dt = datetime.combine(base_date, check_in_time)
                        check_out_dt = datetime.combine(base_date, check_out_time)
                        
                        # Handle cross-day scenarios
                        if check_out_dt < check_in_dt:
                            check_out_dt += timedelta(days=1)
                        
                        # Calculate total minutes worked
                        total_minutes = (check_out_dt - check_in_dt).total_seconds() / 60
                        total_hours = total_minutes / 60
                        
                        # Apply business rules for normal hours
                        if weekday == 6 or self.is_national_holiday(ta_date_str):  # Sunday or National Holiday
                            normal_hours = 0  # All Sunday and national holiday work is overtime
                        elif weekday == 5:  # Saturday
                            normal_hours = min(total_hours, 5.0)  # Max 5 hours normal on Saturday
                        else:  # Monday-Friday (non-holiday)
                            normal_hours = min(total_hours, 7.0)  # Max 7 hours normal on weekdays
                        
                        status = "complete"
                            
                    except Exception as time_error:
                        logger.warning(f"Time parsing error for {employee_id} on {ta_date_str}: {time_error}")
                        normal_hours = 0
                        total_minutes = 0
                        status = "absent"
                        
                elif has_check_in or has_check_out:
                    # Incomplete attendance - employee forgot to check in or out
                    # Assign 7 hours for weekdays, 5 for Saturday, 0 for Sunday/National Holiday
                    if weekday == 6 or self.is_national_holiday(ta_date_str):  # Sunday or National Holiday
                        normal_hours = 0
                    elif weekday == 5:  # Saturday  
                        normal_hours = 5.0
                    else:  # Monday-Friday (non-holiday)
                        normal_hours = 7.0
                    
                    status = "incomplete_needs_verification"  # Special status for pink color
                    total_minutes = normal_hours * 60  # Estimate minutes
                else:
                    # No attendance data at all
                    normal_hours = 0
                    total_minutes = 0
                    status = "absent"
                
                # Get overtime hours from overtime table
                overtime_key = f"{employee_id}_{ta_date_str}"
                overtime_hours = overtime_lookup.get(overtime_key, 0)
                
                # Create enhanced record
                enhanced_record = {
                    'BusCode': record.get('BusCode'),
                    'UserDeviceID': record.get('UserDeviceID'),
                    'UserDeviceName': record.get('UserDeviceName'),
                    'EmployeeID': employee_id,
                    'EmployeeName': record.get('EmployeeName', ''),
                    'Gender': record.get('Gender', ''),
                    'TADate': ta_date,
                    'TACheckIn': check_in,
                    'TACheckOut': check_out,
                    'Shift': record.get('Shift'),
                    'IsCrossDay': record.get('IsCrossDay', False),
                    'TotalMinutesWorked': total_minutes,
                    'RegularHours': normal_hours,
                    'OvertimeHours': overtime_hours,
                    'TotalHours': normal_hours + overtime_hours,
                    'Status': status
                }
                
                combined_results.append(enhanced_record)
            
            logger.info(f"Retrieved {len(combined_results)} attendance records with overtime data")
            return combined_results
            
        except Exception as e:
            logger.error(f"Error retrieving attendance data: {str(e)}")
            return []

    def generate_daily_report(self, date: str, bus_code: str = None) -> str:
        """Generate a daily attendance report."""
        data = self.get_attendance_data(date, date, bus_code)
        if not data:
            return None
        filename = f"daily_report_{date.replace('-', '_')}.xlsx"
        return self.export_manager.export_to_excel(
            data=data, filename=filename, title=f"Daily Attendance Report - {date}"
        )

    def generate_date_range_report(self, start_date: str, end_date: str, bus_code: str = None) -> str:
        """Generate attendance report for a date range."""
        data = self.get_attendance_data(start_date, end_date, bus_code)
        if not data:
            return None
        filename = f"range_report_{start_date.replace('-', '_')}_to_{end_date.replace('-', '_')}.xlsx"
        return self.export_manager.export_to_excel(
            data=data, filename=filename, title=f"Attendance Report - {start_date} to {end_date}"
        )

    def get_employees_list(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get list of employees from the database."""
        query = """
        SELECT DISTINCT
            emp.EmployeeID,
            emp.EmployeeName,
            emp.BusCode,
            emp.Gender
        FROM
            HR_M_EmployeePI emp
        WHERE
            emp.EmployeeID IS NOT NULL
            AND emp.EmployeeName IS NOT NULL
        """
        params = []
        if bus_code:
            query += " AND emp.BusCode = ?"
            params.append(bus_code)
        query += " ORDER BY emp.EmployeeName"

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            logger.info(f"Retrieved {len(results)} employees")
            return results
        except Exception as e:
            logger.error(f"Error retrieving employees list: {str(e)}")
            return []

    def get_shifts_list(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get list of shifts from the database."""
        query = """
        SELECT DISTINCT
            t.Shift,
            COUNT(*) as EmployeeCount
        FROM
            HR_T_TAMachine_Summary t
        WHERE
            t.Shift IS NOT NULL
        """
        params = []
        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)
        query += " GROUP BY t.Shift ORDER BY t.Shift"

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            return results
        except Exception as e:
            logger.error(f"Error retrieving shifts list: {str(e)}")
            return []

    def get_available_months(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get list of available months with attendance data."""
        query = """
        SELECT 
            YEAR(t.TADate) as Year,
            MONTH(t.TADate) as Month,
            DATENAME(MONTH, t.TADate) as MonthName,
            COUNT(*) as RecordCount,
            COUNT(DISTINCT t.EmployeeID) as EmployeeCount,
            MIN(t.TADate) as FirstDate,
            MAX(t.TADate) as LastDate
        FROM 
            HR_T_TAMachine_Summary t
        WHERE 
            t.TADate IS NOT NULL
        """
        params = []
        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)
        query += """
        GROUP BY YEAR(t.TADate), MONTH(t.TADate), DATENAME(MONTH, t.TADate)
        ORDER BY YEAR(t.TADate) DESC, MONTH(t.TADate) DESC
        """

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            return results
        except Exception as e:
            logger.error(f"Error retrieving available months: {str(e)}")
            return []

    def get_monthly_summary(self, year: int, month: int, bus_code: str = None) -> Dict[str, Any]:
        """Get monthly attendance summary statistics."""
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1
        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        query = """
        SELECT 
            COUNT(*) as TotalRecords,
            COUNT(DISTINCT t.EmployeeID) as TotalEmployees,
            AVG(CASE 
                WHEN t.TACheckIn IS NOT NULL AND t.TACheckOut IS NOT NULL 
                THEN DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) / 60.0 
                ELSE 0 
            END) as AvgHoursPerDay,
            SUM(CASE 
                WHEN t.TACheckIn IS NOT NULL AND t.TACheckOut IS NOT NULL 
                THEN DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) / 60.0 
                ELSE 0 
            END) as TotalHours
        FROM 
            HR_T_TAMachine_Summary t
        WHERE 
            t.TADate BETWEEN ? AND ?
        """
        params = [start_date, end_date]
        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            if results:
                summary = results[0]
                summary.update({
                    'Year': year, 'Month': month, 'MonthName': calendar.month_name[month],
                    'StartDate': start_date, 'EndDate': end_date
                })
                return summary
            return {}
        except Exception as e:
            logger.error(f"Error generating monthly summary: {str(e)}")
            return {}

    def get_monthly_attendance_grid(self, year: int, month: int, bus_code: str = None) -> Dict[str, Any]:
        """Generate enhanced monthly attendance grid with normal/overtime separation."""
        start_date = f"{year}-{month:02d}-01"
        days_in_month = calendar.monthrange(year, month)[1]
        end_date = f"{year}-{month:02d}-{days_in_month:02d}"

        data = self.get_attendance_data(start_date, end_date, bus_code)
        employees = self.get_employees_list(bus_code)

        grid_data = []
        employee_no = 1

        for employee in employees:
            employee_id = employee['EmployeeID']
            employee_name = employee['EmployeeName']
            employee_records = [record for record in data if record['EmployeeID'] == employee_id]

            days = {}
            for day in range(1, days_in_month + 1):
                day_date = f"{year}-{month:02d}-{day:02d}"
                day_record = next((r for r in employee_records if r['TADate'].strftime('%Y-%m-%d') == day_date), None)
                
                date_obj = datetime(year, month, day)
                is_sunday = date_obj.weekday() == 6  # Sunday
                is_saturday = date_obj.weekday() == 5  # Saturday
                is_national_holiday = self.is_national_holiday(day_date)
                holiday_info = self.get_holiday_info(day_date) if is_national_holiday else None
                
                if day_record:
                    # Get data from the combined record
                    normal_hours = float(day_record.get('RegularHours', 0))
                    overtime_hours = float(day_record.get('OvertimeHours', 0))
                    record_status = day_record.get('Status', 'unknown')
                    check_in = day_record.get('TACheckIn')
                    check_out = day_record.get('TACheckOut')
                    total_minutes = day_record.get('TotalMinutesWorked', 0)
                    
                    # Determine final status for display
                    if record_status == "incomplete_needs_verification":
                        status = "needs_verification"  # Pink color - needs crosscheck
                    elif record_status == "complete":
                        status = "complete"  # Normal colors based on hours
                    else:
                        status = "absent"
                    
                    # Format the day cell data with ALWAYS showing both normal and overtime
                    days[str(day)] = {
                        'normal_hours': round(normal_hours, 1),
                        'overtime_hours': round(overtime_hours, 1),
                        'status': status,
                        'is_weekend': is_saturday or is_sunday,
                        'is_sunday': is_sunday,
                        'is_saturday': is_saturday,
                        'is_national_holiday': is_national_holiday,
                        'holiday_info': holiday_info,
                        'has_overtime': overtime_hours > 0,
                        'needs_verification': status == "needs_verification",
                        'display_format': 'always_both',  # Always show (normal | overtime)
                        'raw_data': {
                            'check_in': str(check_in) if check_in else None,
                            'check_out': str(check_out) if check_out else None,
                            'total_minutes': total_minutes,
                            'original_status': record_status
                        }
                    }
                else:
                    # No attendance record for this day
                    if is_sunday or is_national_holiday:
                        days[str(day)] = {
                            'normal_hours': 0,
                            'overtime_hours': 0,
                            'status': 'off',
                            'is_weekend': is_sunday or is_saturday,
                            'is_sunday': is_sunday,
                            'is_saturday': is_saturday,
                            'is_national_holiday': is_national_holiday,
                            'holiday_info': holiday_info,
                            'has_overtime': False,
                            'needs_verification': False,
                            'display_format': 'always_both',
                            'raw_data': None
                        }
                    else:
                        days[str(day)] = {
                            'normal_hours': 0,
                            'overtime_hours': 0,
                            'status': 'absent',
                            'is_weekend': is_saturday,
                            'is_sunday': False,
                            'is_saturday': is_saturday,
                            'is_national_holiday': is_national_holiday,
                            'holiday_info': holiday_info,
                            'has_overtime': False,
                            'needs_verification': False,
                            'display_format': 'always_both',
                            'raw_data': None
                        }

            grid_data.append({
                'No': employee_no,
                'EmployeeID': employee_id,
                'EmployeeName': employee_name,
                'days': days
            })
            employee_no += 1

        result = {
            'year': year,
            'month': month,
            'month_name': calendar.month_name[month],
            'days_in_month': days_in_month,
            'total_employees': len(grid_data),
            'grid_data': grid_data,
            'date_range': f"{start_date} to {end_date}",
            'display_format': 'enhanced_always_both'  # Flag to indicate new format
        }

        return result 