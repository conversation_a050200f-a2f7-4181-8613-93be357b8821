# Knowledge Vault: VenusHR14 Attendance System

## Overview
This Knowledge Vault maintains comprehensive project context for the VenusHR14 Attendance Report System. It serves as the primary source of truth for project understanding, progress tracking, and decision history.

## Structure

### Core Files (Required)
These files form the foundation of project knowledge and must be maintained:

#### 📋 `projectbrief.md` → `projectOverview.md`
- **Purpose**: Foundational document defining project scope and objectives
- **Contains**: Project goals, business requirements, success criteria, constraints
- **Updated**: When project scope or requirements change

#### 🎯 `productContext.md`
- **Purpose**: Business context and value proposition
- **Contains**: Problem definition, target users, business value, success metrics
- **Updated**: When business requirements or user needs evolve

#### 🔄 `activeContext.md` → `currentFocus.md`
- **Purpose**: Current development status and active work
- **Contains**: Current session focus, recent changes, immediate next steps
- **Updated**: Every development session

#### 🏗️ `systemPatterns.md` → `systemArchitecture.md`
- **Purpose**: Technical architecture and design patterns
- **Contains**: System structure, component relationships, design patterns
- **Updated**: When architectural decisions are made

#### 🛠️ `techContext.md` → `techStack.md`
- **Purpose**: Technology stack and dependencies
- **Contains**: Technologies used, setup instructions, constraints
- **Updated**: When technologies are added/changed

#### 📊 `progress.md` → `progressTracker.md`
- **Purpose**: Comprehensive progress tracking
- **Contains**: Completed work, in-progress tasks, planned items, known issues
- **Updated**: After every significant change or milestone

### Additional Context Files

#### 📁 `ReadMe/` Directory
Contains detailed documentation for specific features and implementations:
- Feature specifications
- Implementation guides
- Interface changes
- Enhancement documentation

#### 📁 `test/` Directory
Contains test files and validation scripts for system components.

### Project Intelligence

#### 🧠 `.augmentrules`
- **Purpose**: Dynamic learning journal capturing project-specific insights
- **Contains**: Coding patterns, user preferences, known issues, workarounds
- **Updated**: When new patterns or preferences are identified

## File Relationships

```mermaid
flowchart TD
    PO[projectOverview.md] --> PC[productContext.md]
    PO --> SA[systemArchitecture.md]
    PO --> TS[techStack.md]
    
    PC --> CF[currentFocus.md]
    SA --> CF
    TS --> CF
    
    CF --> PT[progressTracker.md]
    
    AR[.augmentrules] --> CF
    AR --> PT
    
    RD[ReadMe/] --> CF
    TEST[test/] --> PT
```

## Usage Guidelines

### For AI Assistants
1. **Always read ALL core files** at the start of every session
2. **Update progressTracker.md** after completing any task
3. **Update currentFocus.md** when changing development focus
4. **Consult .augmentrules** for project-specific patterns
5. **Cross-reference files** to maintain consistency

### For Developers
1. **Review Knowledge Vault** before starting work
2. **Update relevant files** when making changes
3. **Document decisions** in appropriate files
4. **Maintain traceability** between files

## Update Triggers

### Automatic Updates
- After completing any development task
- When encountering new issues or solutions
- When making architectural decisions
- When changing project scope or requirements

### Manual Updates
- Weekly progress reviews
- Milestone completions
- Major feature implementations
- System architecture changes

## Maintenance

### File Validation
- Ensure all core files exist and are current
- Verify cross-references between files
- Check for outdated information
- Validate progress tracking accuracy

### Content Quality
- Keep information concise and relevant
- Use consistent formatting and structure
- Include timestamps for traceability
- Maintain clear section organization

## Current Project Status

### Phase: Critical Issue Resolution
- **Focus**: Resolving "no months displayed" issue
- **Priority**: Database connection and data validation
- **Status**: 75% complete - solutions implemented, testing pending

### Key Components Status
- ✅ **Core Features**: Complete and functional
- 🔄 **Critical Issues**: Under active resolution
- 📋 **Documentation**: Comprehensive and current
- 🧪 **Testing**: Pending issue resolution

## Quick Reference

### Most Important Files for Context
1. `currentFocus.md` - What's happening now
2. `progressTracker.md` - What's been done and what's next
3. `projectOverview.md` - Why this project exists
4. `.augmentrules` - How to work with this project

### Emergency Information
- **Critical Issue**: No months displayed in UI
- **Debug Tools**: `test_months_debug.py`, `/api/debug-months`
- **Key Files**: `web_app.py` (line 2007), `modules/attendance_reporter.py` (lines 185-207)
- **Database**: VenusHR14, Business Code: 'PTRJ'

---
*Knowledge Vault Structure v2.0*  
*Last Updated: 2024-12-31*  
*Maintained by: Augment Code AI Assistant*
